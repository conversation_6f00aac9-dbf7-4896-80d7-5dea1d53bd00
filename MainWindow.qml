pragma ComponentBehavior: Bound
import QtQuick
import QtQuick.Layouts
import QtQml

Rectangle {
    id: mainWindow

//    width: 1200
//    height: 340

    signal showDevices

    property string devName: "..."
    property string frimVer: "..."
    property string mac: "..."
    property string status: "..."
    property string wifi_status: "..."
    property string wifi_signal: "..."
    property string smart_home_Status: "..."
    property int ota_progress: 0


    Connections {
        target: Device

        function onLogToApp(message, color)
        {
            logsModel.append({"log_message": message, "log_entry_color": color})
        }

        function onServiceScanCompleted()
        {
            Device.connectToService("0xf3")
        }

        function onGuid_data_changed(data_type, data)
        {
            switch(data_type) {
                case "device_name":
                    mainWindow.devName = data
                    break
                case "connection_status":
                    if(data === "connected") {
                        mainWindow.status = "connected"
                    } else {
                        mainWindow.status = "disconnected"
                        mainWindow.wifi_status = "disconnected"
                        mainWindow.wifi_signal = ".."
                        mainWindow.smart_home_Status = "disconnected"
                    }
                    break
                case "firm_ver":
                    mainWindow.frimVer = data
                    break
                case "wifi_status":
                    mainWindow.wifi_status = data
                    break
                case "wifi_level":
                    mainWindow.wifi_signal = data
                    break
                case "bsh_connection":
                    mainWindow.smart_home_Status = data
                    break
                case "ota_progress":
                    mainWindow.ota_progress = +data
                    break
            }
        }
    }




    // Top row with device items
    Rectangle {
        id: top_row
        width: parent.width
        height: 38
        color: "#96d0b1"

        RowLayout {
            id: layout
            anchors.fill: parent
            spacing: 6

            MainWindowHeaderItem {
                id: devName
                labelName: "Device name:"
                labelValue: mainWindow.devName
                Layout.fillWidth: true
                Layout.leftMargin: 3
            }

            MainWindowHeaderItem {
                id: firmVer
                labelName: "Firmware ver:"
                labelValue: mainWindow.frimVer
                Layout.fillWidth: true
            }

            MainWindowHeaderItem {
                id: addr
                labelName: "Address:"
                labelValue: mainWindow.mac
                Layout.fillWidth: true
            }

            MainWindowHeaderItem {
                id: status
                labelName: "Status:"
                labelValue: mainWindow.status
                Layout.fillWidth: true
            }
        }
    }

    // Top row with device items
    Rectangle {
        id: top_row2
        width: parent.width
        anchors.top: top_row.bottom
        height: 38
        color: "#96d0b1"
        RowLayout {
            id: layout2
            anchors.fill: parent
            spacing: 6

            MainWindowHeaderItem {
                id: devName2
                labelName: "WIFI: "
                labelValue: mainWindow.wifi_status
                Layout.fillWidth: true
                Layout.leftMargin: 3
            }

            MainWindowHeaderItem {
                id: firmVer2
                labelName: "WIFI signal: "
                labelValue: mainWindow.wifi_signal
                Layout.fillWidth: true
            }

            MainWindowHeaderItem {
                id: addr2
                labelName: "Smart home: "
                labelValue: mainWindow.smart_home_Status
                Layout.fillWidth: true
            }

            MainWindowHeaderItem {
                id: status2
                labelName: "    "
//                labelValue: mainWindow.status
                Layout.fillWidth: true
            }
        }
    }



    // log window
    Rectangle {
        id: logText
        anchors.top: top_row2.bottom
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        width: parent.width/2
        color: "black"

        // logs listview
        // model
        ListModel {
            id: logsModel
            ListElement {
                log_message: " - Started -"
                log_entry_color: "lightgreen"
            }
        }

        // delegate
        component MyDelegate : Text {
            id: log_string_delegate
            required property string log_message
            required property string log_entry_color

//            width: parent.width
            height: 14

            text: log_message
            font.bold: true
            font.pointSize: 10
            color: log_entry_color

//            ListView.onAddChanged: {logsListView.positionViewAtEnd()}
            }

        // listview
        ListView {
            id: logsListView
            anchors.fill: parent
            model: logsModel
            clip: true
            delegate: MyDelegate {}

            onCountChanged: {
                var newIndex = count - 1 // last index
                positionViewAtEnd()
                currentIndex = newIndex
            }
        }

        Rectangle{
            width: 70
            height: 30
            color: "red"
            anchors.right: parent.right
            anchors.bottom: parent.bottom

            JustButton {
                id: clearBtn
                btnText: "Clear"
                anchors.fill: parent
                onButtonClick: {
                    logsModel.clear()
                }
            }
        }

    }


    // controls buttons

    Rectangle {
        signal showDevices

        id: controls

        anchors.left: parent.left
        anchors.bottom: parent.bottom
        anchors.right: logText.left
        anchors.top: top_row2.bottom

        color: "lightgrey"


        BleCmdButton {
            id: setWifiSbtn
            anchors.left: parent.left
            anchors.top: parent.top
            btnText: "Set Wifi ssid"
            onButtonClick: {
                Device.sendToBle("wifi_ssid", setWifiSbtn.btnData)
            }
        }

        BleCmdButton {
            id: setWifiPbtn
            anchors.left: parent.left
            anchors.top: setWifiSbtn.bottom
            btnText: "Set Wifi pswd"
            onButtonClick: {
                Device.sendToBle("wifi_pswd", setWifiPbtn.btnData)
            }
        }

        BleCmdButton {
            id: setMqttUidBtn
            anchors.left: parent.left
            anchors.top: setWifiPbtn.bottom
            btnText: "Set mqtt uid"
            onButtonClick: {
                Device.sendToBle("mqtt_uid", setMqttUidBtn.btnData)
            }
        }

        BleCmdButton {
            id: setMqttPsdBtn
            anchors.left: parent.left
            anchors.top: setMqttUidBtn.bottom
            btnText: "Set mqtt pswd"
            onButtonClick: {
                Device.sendToBle("mqtt_psw", setMqttPsdBtn.btnData)
            }
        }

        BleCmdButton {
            id: setBalLinkBtn
            anchors.left: parent.left
            anchors.top: setMqttPsdBtn.bottom
            btnText: "Set balancer link"
            onButtonClick: {
                Device.sendToBle("blcr_link", setBalLinkBtn.btnData)
            }
        }



        /***************************************
        *     get device info buttons
        ****************************************/
        RowLayout {
            id: getButtonsRow
            width: parent.width
            height: 30
            anchors.top: setWifiSbtn.bottom
            anchors.topMargin: 210
            spacing: 5

            JustButton {
                id: getDvNameBtn
                btnText: "get name"
                onButtonClick: {
                    Device.sendToBle("get_device_name", "")
                }
            }

            JustButton {
                id: getDvIdBtn
                btnText: "get dev id"
                onButtonClick: {
                    Device.sendToBle("get_device_id", "")
                }
            }

            JustButton {
                id: getDvGuidBtn
                btnText: "get GUID(-)"
                onButtonClick: {
                    Device.sendToBle("get_guid", "")
                }
            }

            JustButton {
                id: getDvChalBtn
                btnText: "get challenge"
                onButtonClick: {
                    Device.sendToBle("get_challenge", "")
                }
            }

            JustButton {
                id: getDvChalSignBtn
                btnText: "get c.sign(-)"
                onButtonClick: {
                    // Device.sendToBle("get_challenge_sign", "")
                }
            }

            JustButton {
                id: getBrokerLinkBtn
                btnText: "get broker(-)"
                onButtonClick: {
                    Device.sendToBle("get_broker_link", "")
                }
            }
        }



        RowLayout {
            id: buttons_row_2
            width: parent.width
            height: 30
            anchors.top: getButtonsRow.bottom
            anchors.topMargin: 5
            spacing: 5

            JustButton {
                id: setLogs_on
                btnText: "turn logs on"
                onButtonClick: {
                    Device.sendToBle("turn_logs", "on")
                }
            }
            JustButton {
                id: setLogs_off
                btnText: "turn logs off"
                onButtonClick: {
                    Device.sendToBle("turn_logs", "off")
                }
            }
        }



        /***************************************
        *     connect commands
        ****************************************/
        RowLayout {
            id: connectButtonsRect
            width: parent.width
            height: 30
            anchors.top: buttons_row_2.bottom
            anchors.topMargin: 5
            spacing: 5

            JustButton {
                id: conSHbtn
                btnText: "conn to SH"
                onButtonClick: {
                    Device.sendToBle("conn_s_h", "")
                }
            }

            JustButton {
                id: conWifiBtn
                btnText: "conn to WIFI"
                onButtonClick: {
                    Device.sendToBle("conn_wifi", "")
                }
            }

            JustButton {
                id: getBrokerBtn
                btnText: "conn blcr"
                onButtonClick: {
                    Device.sendToBle("conn_blcr", "")
                }
            }

            JustButton {
                id: canBrokerBtn
                btnText: "conn broker"
                onButtonClick: {
                    Device.sendToBle("conn_broker", "")
                }
            }
        }

        // send to broker button
        BleCmdButton {
            id: sendToBrokerBtn
            anchors.left: parent.left
            anchors.top: connectButtonsRect.bottom
            anchors.topMargin: 5
            btnText: "Send to broker(-)"
            onButtonClick: {
                Device.sendToBle("send_to_broker", sendToBrokerBtn.btnData)
            }
        }

        // fake from broker button
        BleCmdButton {
            id: fakeFromBrokerBtn
            anchors.left: parent.left
            anchors.top: sendToBrokerBtn.bottom
            anchors.topMargin: 5
            btnText: "Fake from broker"
            btnValueHint: "01 01 41 52 41 00 00 01 00 00 04 00 00 00"
            onButtonClick: {
                Device.sendToBle("fake_from_broker", fakeFromBrokerBtn.btnData)
            }
        }

        // send to uart button
        BleCmdButton {
            id: sendToUartBtn
            anchors.left: parent.left
            anchors.top: fakeFromBrokerBtn.bottom
            anchors.topMargin: 5
            btnText: "Send to UART"
            btnValueHint: "55 AA"
            onButtonClick: {
                Device.sendToBle("uart_pkt", sendToUartBtn.btnData)
            }
        }

        BleCmdButton {
            id: frwm_with_link
            anchors.left: parent.left
            anchors.top: sendToUartBtn.bottom
            anchors.topMargin: 5
            btnText: "Firmware update"
            btnValueHint: "http://..."
            onButtonClick: {
                Device.sendToBle("ota_with_link", frwm_with_link.btnData)
            }
        }

        // test and other buttons
        RowLayout {
            id: testButtonsRow
            width: parent.width
            height: 30
            anchors.top: frwm_with_link.bottom
            anchors.topMargin: 5
            spacing: 5


            JustButton {
                id: testBtn
                btnText: "test command"
                onButtonClick: {
                    Device.sendToBle("run_test", "")
                }
            }

            JustButton {
                id: startOtaDebBtn
                btnText: "Firm upd hard"
                onButtonClick: {
                    Device.sendToBle("ota", "")
                }
            }

            JustButton {
                id: emulator_on_btn
                btnText: "emulator on"
                onButtonClick: {
                    Device.sendToBle("emulator", "on")
                }
            }

            JustButton {
                id: emulator_off_btn
                btnText: "emulator off"
                onButtonClick: {
                    Device.sendToBle("emulator", "off")
                }
            }
        }



        // bottom buttons
        BottomButton {
            id: restEspBtn
            anchors.bottom: parent.bottom
            anchors.left: parent.left
            anchors.leftMargin: 5
            btnText: "Reset ESP"
            onButtonClick: {
                Device.sendToBle("reset","")
            }
        }

        BottomButton {
            id: connectBtn
            anchors.bottom: parent.bottom
            anchors.left: restEspBtn.right
            anchors.leftMargin: 5
            btnText: "Dis/connect"

            onButtonClick: {
                Device.reconnectToA830()
            }
        }

        BottomButton {
            id: backBtn
            anchors.bottom: parent.bottom
            anchors.left: connectBtn.right
            anchors.leftMargin: 5
            btnText: "Back to scanner"

            onButtonClick: {
                Device.disconnectFromDevice()
                showDevices()
                Device.update = "Search"
            }
        }
    }
}
