<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE QtCreatorProject>
<!-- Written by QtCreator 11.0.1, 2025-05-21T09:15:36. -->
<qtcreator>
 <data>
  <variable>EnvironmentId</variable>
  <value type="QByteArray">{f610d814-836d-4b28-b3ca-eb4ee6427e2f}</value>
 </data>
 <data>
  <variable>ProjectExplorer.Project.ActiveTarget</variable>
  <value type="qlonglong">0</value>
 </data>
 <data>
  <variable>ProjectExplorer.Project.EditorSettings</variable>
  <valuemap type="QVariantMap">
   <value type="bool" key="EditorConfiguration.AutoIndent">true</value>
   <value type="bool" key="EditorConfiguration.AutoSpacesForTabs">false</value>
   <value type="bool" key="EditorConfiguration.CamelCaseNavigation">true</value>
   <valuemap type="QVariantMap" key="EditorConfiguration.CodeStyle.0">
    <value type="QString" key="language">Cpp</value>
    <valuemap type="QVariantMap" key="value">
     <value type="QByteArray" key="CurrentPreferences">CppGlobal</value>
    </valuemap>
   </valuemap>
   <valuemap type="QVariantMap" key="EditorConfiguration.CodeStyle.1">
    <value type="QString" key="language">QmlJS</value>
    <valuemap type="QVariantMap" key="value">
     <value type="QByteArray" key="CurrentPreferences">QmlJSGlobal</value>
    </valuemap>
   </valuemap>
   <value type="qlonglong" key="EditorConfiguration.CodeStyle.Count">2</value>
   <value type="QByteArray" key="EditorConfiguration.Codec">UTF-8</value>
   <value type="bool" key="EditorConfiguration.ConstrainTooltips">false</value>
   <value type="int" key="EditorConfiguration.IndentSize">4</value>
   <value type="bool" key="EditorConfiguration.KeyboardTooltips">false</value>
   <value type="int" key="EditorConfiguration.MarginColumn">80</value>
   <value type="bool" key="EditorConfiguration.MouseHiding">true</value>
   <value type="bool" key="EditorConfiguration.MouseNavigation">true</value>
   <value type="int" key="EditorConfiguration.PaddingMode">1</value>
   <value type="bool" key="EditorConfiguration.PreferSingleLineComments">false</value>
   <value type="bool" key="EditorConfiguration.ScrollWheelZooming">true</value>
   <value type="bool" key="EditorConfiguration.ShowMargin">false</value>
   <value type="int" key="EditorConfiguration.SmartBackspaceBehavior">0</value>
   <value type="bool" key="EditorConfiguration.SmartSelectionChanging">true</value>
   <value type="bool" key="EditorConfiguration.SpacesForTabs">true</value>
   <value type="int" key="EditorConfiguration.TabKeyBehavior">0</value>
   <value type="int" key="EditorConfiguration.TabSize">8</value>
   <value type="bool" key="EditorConfiguration.UseGlobal">true</value>
   <value type="bool" key="EditorConfiguration.UseIndenter">false</value>
   <value type="int" key="EditorConfiguration.Utf8BomBehavior">1</value>
   <value type="bool" key="EditorConfiguration.addFinalNewLine">true</value>
   <value type="bool" key="EditorConfiguration.cleanIndentation">true</value>
   <value type="bool" key="EditorConfiguration.cleanWhitespace">true</value>
   <value type="QString" key="EditorConfiguration.ignoreFileTypes">*.md, *.MD, Makefile</value>
   <value type="bool" key="EditorConfiguration.inEntireDocument">false</value>
   <value type="bool" key="EditorConfiguration.skipTrailingWhitespace">true</value>
   <value type="bool" key="EditorConfiguration.tintMarginArea">true</value>
  </valuemap>
 </data>
 <data>
  <variable>ProjectExplorer.Project.PluginSettings</variable>
  <valuemap type="QVariantMap">
   <valuemap type="QVariantMap" key="AutoTest.ActiveFrameworks">
    <value type="bool" key="AutoTest.Framework.Boost">true</value>
    <value type="bool" key="AutoTest.Framework.CTest">false</value>
    <value type="bool" key="AutoTest.Framework.Catch">true</value>
    <value type="bool" key="AutoTest.Framework.GTest">true</value>
    <value type="bool" key="AutoTest.Framework.QtQuickTest">true</value>
    <value type="bool" key="AutoTest.Framework.QtTest">true</value>
   </valuemap>
   <valuemap type="QVariantMap" key="AutoTest.CheckStates"/>
   <value type="int" key="AutoTest.RunAfterBuild">0</value>
   <value type="bool" key="AutoTest.UseGlobal">true</value>
   <valuemap type="QVariantMap" key="ClangTools">
    <value type="bool" key="ClangTools.AnalyzeOpenFiles">true</value>
    <value type="bool" key="ClangTools.BuildBeforeAnalysis">true</value>
    <value type="QString" key="ClangTools.DiagnosticConfig">Builtin.DefaultTidyAndClazy</value>
    <value type="int" key="ClangTools.ParallelJobs">6</value>
    <value type="bool" key="ClangTools.PreferConfigFile">true</value>
    <valuelist type="QVariantList" key="ClangTools.SelectedDirs"/>
    <valuelist type="QVariantList" key="ClangTools.SelectedFiles"/>
    <valuelist type="QVariantList" key="ClangTools.SuppressedDiagnostics"/>
    <value type="bool" key="ClangTools.UseGlobalSettings">true</value>
   </valuemap>
   <valuemap type="QVariantMap" key="CppEditor.QuickFix">
    <value type="bool" key="UseGlobalSettings">true</value>
   </valuemap>
  </valuemap>
 </data>
 <data>
  <variable>ProjectExplorer.Project.Target.0</variable>
  <valuemap type="QVariantMap">
   <value type="QString" key="DeviceType">Desktop</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Desktop Qt 6.5.2 MSVC2019 64bit</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Desktop Qt 6.5.2 MSVC2019 64bit</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">qt.qt6.652.win64_msvc2019_64_kit</value>
   <value type="qlonglong" key="ProjectExplorer.Target.ActiveBuildConfiguration">0</value>
   <value type="qlonglong" key="ProjectExplorer.Target.ActiveDeployConfiguration">0</value>
   <value type="qlonglong" key="ProjectExplorer.Target.ActiveRunConfiguration">0</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.0">
    <value type="int" key="EnableQmlDebugging">0</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">D:\temp\2\build-lowenergyscanner-Desktop_Qt_6_5_2_MSVC2019_64bit-Debug</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory.shadowDir">D:/temp/2/build-lowenergyscanner-Desktop_Qt_6_5_2_MSVC2019_64bit-Debug</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QtProjectManager.QMakeBuildStep</value>
      <value type="bool" key="QtProjectManager.QMakeBuildStep.QMakeForced">false</value>
      <valuelist type="QVariantList" key="QtProjectManager.QMakeBuildStep.SelectedAbis"/>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.1">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">2</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
      <value type="QString" key="Qt4ProjectManager.MakeStep.MakeArguments">clean</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.CustomParsers"/>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ParseStandardOutput">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Debug</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.Qt4BuildConfiguration</value>
    <value type="int" key="Qt4ProjectManager.Qt4BuildConfiguration.BuildConfiguration">2</value>
   </valuemap>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.1">
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">D:\temp\2\build-lowenergyscanner-Desktop_Qt_6_5_2_MSVC2019_64bit-Release</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory.shadowDir">D:/temp/2/build-lowenergyscanner-Desktop_Qt_6_5_2_MSVC2019_64bit-Release</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QtProjectManager.QMakeBuildStep</value>
      <value type="bool" key="QtProjectManager.QMakeBuildStep.QMakeForced">false</value>
      <valuelist type="QVariantList" key="QtProjectManager.QMakeBuildStep.SelectedAbis"/>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.1">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">2</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
      <value type="QString" key="Qt4ProjectManager.MakeStep.MakeArguments">clean</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.CustomParsers"/>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ParseStandardOutput">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Release</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.Qt4BuildConfiguration</value>
    <value type="int" key="Qt4ProjectManager.Qt4BuildConfiguration.BuildConfiguration">0</value>
    <value type="int" key="QtQuickCompiler">0</value>
   </valuemap>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.2">
    <value type="int" key="EnableQmlDebugging">0</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">D:\temp\2\build-lowenergyscanner-Desktop_Qt_6_5_2_MSVC2019_64bit-Profile</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory.shadowDir">D:/temp/2/build-lowenergyscanner-Desktop_Qt_6_5_2_MSVC2019_64bit-Profile</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QtProjectManager.QMakeBuildStep</value>
      <value type="bool" key="QtProjectManager.QMakeBuildStep.QMakeForced">false</value>
      <valuelist type="QVariantList" key="QtProjectManager.QMakeBuildStep.SelectedAbis"/>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.1">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">2</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
      <value type="QString" key="Qt4ProjectManager.MakeStep.MakeArguments">clean</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.CustomParsers"/>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ParseStandardOutput">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Profile</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.Qt4BuildConfiguration</value>
    <value type="int" key="Qt4ProjectManager.Qt4BuildConfiguration.BuildConfiguration">0</value>
    <value type="int" key="QtQuickCompiler">0</value>
    <value type="int" key="SeparateDebugInfo">0</value>
   </valuemap>
   <value type="qlonglong" key="ProjectExplorer.Target.BuildConfigurationCount">3</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.DeployConfiguration.0">
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">0</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Deploy</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Deploy</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Deploy</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">1</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.DeployConfiguration.CustomData"/>
    <value type="bool" key="ProjectExplorer.DeployConfiguration.CustomDataEnabled">false</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.DefaultDeployConfiguration</value>
   </valuemap>
   <value type="qlonglong" key="ProjectExplorer.Target.DeployConfigurationCount">1</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.RunConfiguration.0">
    <value type="bool" key="Analyzer.Perf.Settings.UseGlobalSettings">true</value>
    <value type="bool" key="Analyzer.QmlProfiler.Settings.UseGlobalSettings">true</value>
    <value type="bool" key="Analyzer.Valgrind.Settings.UseGlobalSettings">true</value>
    <valuelist type="QVariantList" key="CustomOutputParsers"/>
    <value type="int" key="PE.EnvironmentAspect.Base">2</value>
    <valuelist type="QVariantList" key="PE.EnvironmentAspect.Changes"/>
    <value type="bool" key="PE.EnvironmentAspect.PrintOnRun">false</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">lowenergyscanner2</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.Qt4RunConfiguration:D:/Dev/QT/lowenergyscanner/lowenergyscanner.pro</value>
    <value type="QString" key="ProjectExplorer.RunConfiguration.BuildKey">D:/Dev/QT/lowenergyscanner/lowenergyscanner.pro</value>
    <value type="bool" key="RunConfiguration.UseCppDebuggerAuto">true</value>
    <value type="bool" key="RunConfiguration.UseLibrarySearchPath">true</value>
    <value type="bool" key="RunConfiguration.UseQmlDebuggerAuto">true</value>
    <value type="QString" key="RunConfiguration.WorkingDirectory.default">D:/temp/2/build-lowenergyscanner-Desktop_Qt_6_5_2_MSVC2019_64bit-Debug</value>
   </valuemap>
   <value type="qlonglong" key="ProjectExplorer.Target.RunConfigurationCount">1</value>
  </valuemap>
 </data>
 <data>
  <variable>ProjectExplorer.Project.Target.1</variable>
  <valuemap type="QVariantMap">
   <value type="QString" key="DeviceType">Android.Device.Type</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Android Qt 6.5.2 Clang arm64-v8a</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Android Qt 6.5.2 Clang arm64-v8a</value>
   <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">{8b298ed1-c92e-40d0-a7a8-47814180ce8b}</value>
   <value type="qlonglong" key="ProjectExplorer.Target.ActiveBuildConfiguration">0</value>
   <value type="qlonglong" key="ProjectExplorer.Target.ActiveDeployConfiguration">0</value>
   <value type="qlonglong" key="ProjectExplorer.Target.ActiveRunConfiguration">0</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.0">
    <value type="int" key="EnableQmlDebugging">0</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">D:\Dev\QT\build-lowenergyscanner-Android_Qt_6_5_2_Clang_arm64_v8a-Debug</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory.shadowDir">D:/Dev/QT/build-lowenergyscanner-Android_Qt_6_5_2_Clang_arm64_v8a-Debug</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QtProjectManager.QMakeBuildStep</value>
      <value type="bool" key="QtProjectManager.QMakeBuildStep.QMakeForced">false</value>
      <valuelist type="QVariantList" key="QtProjectManager.QMakeBuildStep.SelectedAbis"/>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.1">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.2">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Copy application data</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.AndroidPackageInstallationStep</value>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.3">
      <value type="QString" key="BuildTargetSdk">android-33</value>
      <value type="QString" key="BuildToolsVersion"></value>
      <value type="QString" key="KeystoreLocation"></value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build Android APK</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QmakeProjectManager.AndroidBuildApkStep</value>
      <value type="bool" key="VerboseOutput">false</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">4</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
      <value type="QString" key="Qt4ProjectManager.MakeStep.MakeArguments">clean</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.CustomParsers"/>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ParseStandardOutput">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Debug</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.Qt4BuildConfiguration</value>
    <value type="int" key="Qt4ProjectManager.Qt4BuildConfiguration.BuildConfiguration">2</value>
   </valuemap>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.1">
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">D:\Dev\QT\build-lowenergyscanner-Android_Qt_6_5_2_Clang_arm64_v8a-Release</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory.shadowDir">D:/Dev/QT/build-lowenergyscanner-Android_Qt_6_5_2_Clang_arm64_v8a-Release</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QtProjectManager.QMakeBuildStep</value>
      <value type="bool" key="QtProjectManager.QMakeBuildStep.QMakeForced">false</value>
      <valuelist type="QVariantList" key="QtProjectManager.QMakeBuildStep.SelectedAbis"/>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.1">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.2">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Copy application data</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.AndroidPackageInstallationStep</value>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.3">
      <value type="QString" key="BuildTargetSdk">android-33</value>
      <value type="QString" key="BuildToolsVersion"></value>
      <value type="QString" key="KeystoreLocation"></value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build Android APK</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QmakeProjectManager.AndroidBuildApkStep</value>
      <value type="bool" key="VerboseOutput">false</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">4</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
      <value type="QString" key="Qt4ProjectManager.MakeStep.MakeArguments">clean</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.CustomParsers"/>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ParseStandardOutput">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Release</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.Qt4BuildConfiguration</value>
    <value type="int" key="Qt4ProjectManager.Qt4BuildConfiguration.BuildConfiguration">0</value>
   </valuemap>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.BuildConfiguration.2">
    <value type="int" key="EnableQmlDebugging">0</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory">D:\Dev\QT\build-lowenergyscanner-Android_Qt_6_5_2_Clang_arm64_v8a-Profile</value>
    <value type="QString" key="ProjectExplorer.BuildConfiguration.BuildDirectory.shadowDir">D:/Dev/QT/build-lowenergyscanner-Android_Qt_6_5_2_Clang_arm64_v8a-Profile</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QtProjectManager.QMakeBuildStep</value>
      <value type="bool" key="QtProjectManager.QMakeBuildStep.QMakeForced">false</value>
      <valuelist type="QVariantList" key="QtProjectManager.QMakeBuildStep.SelectedAbis"/>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.1">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.2">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Copy application data</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.AndroidPackageInstallationStep</value>
     </valuemap>
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.3">
      <value type="QString" key="BuildTargetSdk">android-33</value>
      <value type="QString" key="BuildToolsVersion"></value>
      <value type="QString" key="KeystoreLocation"></value>
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build Android APK</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">QmakeProjectManager.AndroidBuildApkStep</value>
      <value type="bool" key="VerboseOutput">false</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">4</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Build</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Build</value>
    </valuemap>
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.1">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.MakeStep</value>
      <value type="QString" key="Qt4ProjectManager.MakeStep.MakeArguments">clean</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Clean</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Clean</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">2</value>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ClearSystemEnvironment">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.CustomParsers"/>
    <value type="bool" key="ProjectExplorer.BuildConfiguration.ParseStandardOutput">false</value>
    <valuelist type="QVariantList" key="ProjectExplorer.BuildConfiguration.UserEnvironmentChanges"/>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Profile</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.Qt4BuildConfiguration</value>
    <value type="int" key="Qt4ProjectManager.Qt4BuildConfiguration.BuildConfiguration">0</value>
    <value type="int" key="SeparateDebugInfo">0</value>
   </valuemap>
   <value type="qlonglong" key="ProjectExplorer.Target.BuildConfigurationCount">3</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.DeployConfiguration.0">
    <valuemap type="QVariantMap" key="ProjectExplorer.BuildConfiguration.BuildStepList.0">
     <valuemap type="QVariantMap" key="ProjectExplorer.BuildStepList.Step.0">
      <value type="bool" key="ProjectExplorer.BuildStep.Enabled">true</value>
      <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.AndroidDeployQtStep</value>
     </valuemap>
     <value type="qlonglong" key="ProjectExplorer.BuildStepList.StepsCount">1</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DefaultDisplayName">Deploy</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.DisplayName">Deploy</value>
     <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">ProjectExplorer.BuildSteps.Deploy</value>
    </valuemap>
    <value type="int" key="ProjectExplorer.BuildConfiguration.BuildStepListCount">1</value>
    <valuemap type="QVariantMap" key="ProjectExplorer.DeployConfiguration.CustomData"/>
    <value type="bool" key="ProjectExplorer.DeployConfiguration.CustomDataEnabled">false</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.AndroidDeployConfiguration2</value>
   </valuemap>
   <value type="qlonglong" key="ProjectExplorer.Target.DeployConfigurationCount">1</value>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.PluginSettings">
    <valuelist type="QVariantList" key="AndroidDeviceAbis">
     <value type="QString">arm64-v8a</value>
     <value type="QString">armeabi-v7a</value>
     <value type="QString">armeabi</value>
    </valuelist>
    <value type="QString" key="AndroidDeviceSerialNumber">TOQSFIQKMBKF4D5X</value>
    <value type="int" key="AndroidVersion.ApiLevel">33</value>
   </valuemap>
   <valuemap type="QVariantMap" key="ProjectExplorer.Target.RunConfiguration.0">
    <value type="bool" key="Analyzer.Perf.Settings.UseGlobalSettings">true</value>
    <value type="bool" key="Analyzer.QmlProfiler.Settings.UseGlobalSettings">true</value>
    <value type="bool" key="Analyzer.Valgrind.Settings.UseGlobalSettings">true</value>
    <valuelist type="QVariantList" key="Android.PostStartShellCmdListKey">
     <value type="QString"></value>
    </valuelist>
    <valuelist type="QVariantList" key="Android.PreStartShellCmdListKey">
     <value type="QString"></value>
    </valuelist>
    <valuelist type="QVariantList" key="CustomOutputParsers"/>
    <value type="int" key="PE.EnvironmentAspect.Base">0</value>
    <valuelist type="QVariantList" key="PE.EnvironmentAspect.Changes"/>
    <value type="bool" key="PE.EnvironmentAspect.PrintOnRun">false</value>
    <value type="QString" key="ProjectExplorer.ProjectConfiguration.Id">Qt4ProjectManager.AndroidRunConfiguration:D:/Dev/QT/lowenergyscanner/lowenergyscanner.pro</value>
    <value type="QString" key="ProjectExplorer.RunConfiguration.BuildKey">D:/Dev/QT/lowenergyscanner/lowenergyscanner.pro</value>
    <value type="bool" key="RunConfiguration.UseCppDebuggerAuto">true</value>
    <value type="bool" key="RunConfiguration.UseQmlDebuggerAuto">true</value>
   </valuemap>
   <value type="qlonglong" key="ProjectExplorer.Target.RunConfigurationCount">1</value>
  </valuemap>
 </data>
 <data>
  <variable>ProjectExplorer.Project.TargetCount</variable>
  <value type="qlonglong">2</value>
 </data>
 <data>
  <variable>ProjectExplorer.Project.Updater.FileVersion</variable>
  <value type="int">22</value>
 </data>
 <data>
  <variable>Version</variable>
  <value type="int">22</value>
 </data>
</qtcreator>
